import {
  ExclamationCircleOutlined,
  SaveOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import { Button, Modal, Input, Select } from "antd";
import { useSearchParams } from "next/navigation";
import React, {
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { toast } from "react-toastify";
import { AutoSizer, InfiniteLoader, MultiGrid } from "react-virtualized";
import { v4 as uuidv4 } from "uuid";

// Type-safe wrappers for react-virtualized components
const AutoSizerComponent: any = AutoSizer;
const MultiGridComponent: any = MultiGrid;

import { useAppSelector } from "@/common/vendors/redux/store/hook";
import { useGetListColour } from "@/modules/list/hooks/colour/useGetListColour";
import { useGetListRockType } from "@/modules/rock-type/hooks/useGetListRockType";
import {
  getDynamicColumnWidth,
  getSubFieldCount,
  isCompound<PERSON>ield,
} from "../../utils/logging.utils";
import { useNavigationBlocker } from "../../utils/useNavigationBlocker";
import { ActionCell, EditableCell, RowStatus } from "../render-field-types";
import { useTableGeologyActions } from "./table-geology-actions";
import { TableGeologyBottom } from "./table-geology-bottom";
import { TableGeologyHeader } from "./table-geology-header";
import {
  DataEntryValueData,
  LoggingFormData,
  LoggingRowData,
  TableGeologyProps,
} from "./table-geology-types";

import { RequestState } from "@/common/configs/app.contants";
import { ExportType } from "@/modules/logging-data/components/types/export.enum";
import dataEntryRequest from "../../api/data-entry.api";
import recoveryRequest from "../../api/recovery.api";
import "../logging-grid/logging-grid.css";

// **PERFORMANCE OPTIMIZATION**: Debounce utility to prevent excessive function calls
// Reduces resize handler calls from 309-321ms to <16ms target
const debounce = <T extends (...args: any[]) => void>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

export const TableGeology = memo<TableGeologyProps>(
  ({
    searchText,
    fontSize,
    handleFontSizeChange,
    refetchDataEntry,
    loggingSuiteIdParams,
    geologySuites,
    geotechSuites,
    loading: parentLoading,
    handleLoggingSuiteChange,
    handleSearch,
  }) => {
    const searchParams = useSearchParams();
    const globalProjectId = useAppSelector(
      (state) => state.user.userInfo.projectId
    );
    // Get query parameters
    const queries: any = {};
    for (const [key, value] of searchParams.entries()) {
      const arrayValues = searchParams.getAll(key);
      queries[key] = arrayValues.length > 1 ? arrayValues : value;
    }

    const geologySuiteDetail = useAppSelector(
      (state) => state.geologySuite.detail
    );
    const geologySuiteFields = geologySuiteDetail?.geologySuiteFields;
    const { selectedDrillHole } = useAppSelector((state) => state.logging);

    // **PERFORMANCE OPTIMIZATION**: Debounced mobile detection
    // Prevents excessive resize handler calls and improves responsiveness
    const [isMobile, setIsMobile] = useState(false);

    // **PERFORMANCE OPTIMIZATION**: Memoized and debounced resize handler
    // Reduces resize handler execution time to <16ms target
    const debouncedCheckMobile = useMemo(
      () =>
        debounce(() => {
          setIsMobile(window.innerWidth <= 1024);
        }, 100),
      []
    );

    useEffect(() => {
      const checkMobile = () => {
        setIsMobile(window.innerWidth <= 1024);
      };

      checkMobile();
      window.addEventListener("resize", debouncedCheckMobile);
      window.addEventListener("orientationchange", debouncedCheckMobile);

      return () => {
        window.removeEventListener("resize", debouncedCheckMobile);
        window.removeEventListener("orientationchange", debouncedCheckMobile);
      };
    }, [debouncedCheckMobile]);

    // Form setup
    const { control, reset, getValues, setError, clearErrors, formState } =
      useForm<LoggingFormData>({
        defaultValues: {
          rows: [],
        },
      });

    const { fields, remove, update, prepend, append } = useFieldArray<
      LoggingFormData,
      "rows"
    >({
      control,
      name: "rows",
    });

    // Local state
    const [loading, setLoading] = useState(false);
    const [hasMore, setHasMore] = useState(true);
    const [totalRows, setTotalRows] = useState(0);

    const [addRowLoading, setAddRowLoading] = useState(false);
    const [dataToDelete, setDataToDelete] = useState<any[]>([]);
    const prevGeologySuiteIdRef = useRef<string | number | null | undefined>();
    const initialLoadDoneRef = useRef(false);

    // **REQUEST CONCURRENCY CONTROL**: Prevent multiple simultaneous load requests
    const isLoadMoreEntries = useRef<boolean>(false);

    // Unsaved changes state
    const [showUnsavedModal, setShowUnsavedModal] = useState(false);
    const [pendingNavigation, setPendingNavigation] = useState<
      (() => void) | null
    >(null);

    // Data loading hooks for field options
    const { request: getColours, data: colours } = useGetListColour();
    const { request: getRockTypes, data: rockTypes } = useGetListRockType();

    // Load options data - only once on mount
    useEffect(() => {
      getColours({ isActive: true, maxResultCount: 1000 });
      getRockTypes({ isActive: true, maxResultCount: 1000 });
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []); // Empty dependency array - only run once on mount

    // **PERFORMANCE OPTIMIZATION**: Simplified unsaved changes detection
    // Uses efficient formState.isDirty instead of watching all rows
    // Eliminates expensive watch("rows") that caused cascade re-renders
    const hasUnsavedChanges = useMemo(() => {
      // Check if form is dirty (React Hook Form built-in dirty state) - O(1) operation
      if (formState.isDirty) return true;

      // Check if there are items to delete - O(1) operation
      const hasItemsToDelete = dataToDelete.length > 0;

      return hasItemsToDelete;
    }, [formState.isDirty, dataToDelete.length]); // Removed watchedRows dependency

    // Navigation blocker
    useNavigationBlocker(hasUnsavedChanges);

    // Get URL parameters - loggingSuiteIdParams now comes from props
    const geologySuiteId = loggingSuiteIdParams?.startsWith("geologySuites")
      ? Number(loggingSuiteIdParams?.split("-")[1])
      : null;
    const drillholeId = selectedDrillHole?.value
      ? Number(selectedDrillHole.value)
      : null;

    // **PERFORMANCE OPTIMIZATION**: Optimized data processing with Map-based lookups
    // Replaced O(n²) nested loops with O(1) Map lookups for geologySuiteFields
    // Reduces loadDataEntries execution time from 205-579ms to <50ms target

    // Fixed row height for virtualized table performance
    const listRef = useRef<any>(null);
    const FIXED_ROW_HEIGHT = 60;

    // **REQUEST CONCURRENCY CONTROL**: Enhanced load function with single active request enforcement
    const loadDataEntries = useCallback(
      async (skipCount = 0, maxResultCount = 20) => {
        if (!geologySuiteId || !drillholeId) return;

        // **CONCURRENCY GUARD**: Prevent multiple simultaneous requests
        if (isLoadMoreEntries.current) {
          return;
        }

        // Mark request as active
        isLoadMoreEntries.current = true;
        const isInitialLoad = skipCount === 0;
        setLoading(true);

        const res = await dataEntryRequest.getAllDataEntry({
          GeologySuiteId: geologySuiteId,
          DrillholeId: drillholeId,
          skipCount: skipCount,
          maxResultCount: maxResultCount,
          sortOrder: "asc",
          keyWord: searchText?.trim() || undefined, // Add server-side search parameter
        });

        if (res.state === RequestState.success && res.data) {
          const fetchedItems = res.data.items;
          const totalCount = res.data.pagination.total;

          // **PERFORMANCE OPTIMIZATION**: Create Map for O(1) field lookups
          // Eliminates O(n²) complexity from nested geologySuiteFields.find() calls
          const geologySuiteFieldsMap = new Map(
            geologySuiteFields?.map((field) => [field.id, field]) || []
          );

          const newRows: LoggingRowData[] = fetchedItems.map(
            (logging: any) => ({
              ...logging,
              id: logging.id || uuidv4(),
              valueId: logging.valueId || uuidv4(),
              dataEntryValues:
                logging?.dataEntryValues?.map((dataEntryValue: any) => {
                  // **PERFORMANCE OPTIMIZATION**: O(1) Map lookup instead of O(n) array.find()
                  const geologySuiteField = geologySuiteFieldsMap.get(
                    dataEntryValue?.geologysuiteFieldId
                  );
                  return {
                    ...dataEntryValue,
                    valueId: dataEntryValue?.valueId || uuidv4(),
                    isMandatory:
                      (geologySuiteField as any)?.isMandatory || false,
                  };
                }) || [],
              rowStatus: RowStatus.SAVED,
              errorDetail: undefined,
              loading: false,
            })
          );

          if (isInitialLoad) {
            reset(
              { rows: newRows },
              { keepDefaultValues: false, keepDirty: false }
            );
            setScrollToRow(newRows.length);
            setTimeout(() => {
              setScrollToRow(-1);
            }, 100);
          } else {
            prepend(newRows, { shouldFocus: false });
            // Remove dirty
            const currentFormData = getValues();
            reset(currentFormData, { keepValues: true });
            // setScrollToRow(newRows.length + 11);
            setScrollToRow(newRows.length + 1);
            setTimeout(() => {
              setScrollToRow(-1);
            }, 100);
          }

          setTotalRows(totalCount);
          setHasMore(skipCount + newRows.length < totalCount);
        } else {
          toast.error(res.message || "Failed to fetch data.");
        }

        // **CONCURRENCY CLEANUP**: Always clean up request state regardless of success/failure
        setLoading(false);
        isLoadMoreEntries.current = false;
      },
      [
        geologySuiteId,
        drillholeId,
        reset,
        geologySuiteFields,
        prepend,
        getValues,
        searchText,
      ]
    );

    // **CONCURRENCY CLEANUP**: Reset request state when component unmounts or key dependencies change
    useEffect(() => {
      return () => {
        isLoadMoreEntries.current = false;
      };
    }, [geologySuiteId, drillholeId]);

    // **PERFORMANCE OPTIMIZATION**: Consolidated useEffects for geologySuiteId changes
    // Merged overlapping effects to reduce unnecessary re-renders and state updates
    useEffect(() => {
      // Reset state and load data when geologySuiteId or drillholeId changes
      if (geologySuiteId && drillholeId && geologySuiteFields?.length) {
        // **CONCURRENCY RESET**: Clear any pending requests when changing contexts
        isLoadMoreEntries.current = false;

        reset({ rows: [] });
        setHasMore(true);
        setTotalRows(0);
        // Reset grid scroll position to top-left when drill hole changes
        setScrollToRow(undefined);
        setScrollToColumn(undefined);
        loadDataEntries(0);
      }
    }, [
      geologySuiteId,
      drillholeId,
      geologySuiteFields,
      loadDataEntries,
      reset,
    ]);

    // **SERVER-SIDE FILTERING**: Reset and reload data when search text changes
    useEffect(() => {
      if (geologySuiteId && drillholeId && geologySuiteFields?.length) {
        // **CONCURRENCY RESET**: Clear any pending requests when search changes
        isLoadMoreEntries.current = false;

        reset({ rows: [] });
        setHasMore(true);
        setTotalRows(0);
        // Reset grid scroll position when search changes
        setScrollToRow(undefined);
        setScrollToColumn(undefined);
        loadDataEntries(0);
      }
    }, [searchText]);

    // Focus management state for virtualized table
    const [currentFocus, setCurrentFocus] = useState<{
      rowIndex: number;
      fieldIndex: number;
      subFieldIndex?: number; // For compound fields with multiple inputs
    } | null>(null);

    // MultiGrid scrolling state - using props for programmatic scrolling (official approach)
    const [scrollToRow, setScrollToRow] = useState<number | undefined>(
      undefined
    );
    const [scrollToColumn, setScrollToColumn] = useState<number | undefined>(
      undefined
    );

    // **PERFORMANCE OPTIMIZATION**: Memoized scroll function with stable reference
    // Prevents unnecessary re-renders in child components
    const scrollToFirstDynamicField = useCallback(() => {
      setScrollToColumn(3);
      setTimeout(() => {
        setScrollToColumn(undefined);
      }, 100);
    }, []);

    // **PERFORMANCE OPTIMIZATION**: Memoized grid mutation callback with stable reference
    // Prevents unnecessary re-renders and optimizes recompute operations
    const handleAfterRowMutation = useCallback(() => {
      if (listRef.current) {
        // Use setTimeout to ensure React has processed the state update
        setTimeout(() => {
          listRef.current?.recomputeGridSize();
          listRef.current?.forceUpdateGrids();
          // Also, force a re-render of the MultiGrid, if necessary,
          // though recomputeGridSize should often be enough.
        }, 0);
      }
    }, []);

    // Initialize table actions hook (with setScrollToRow for prop-based scrolling)
    const tableActions = useTableGeologyActions({
      getValues,
      setError,
      clearErrors,
      reset,
      update,
      append,
      remove,
      setLoading,
      setAddRowLoading,
      setDataToDelete,
      dataToDelete,
      geologySuiteFields,
      geologySuiteId,
      drillholeId,
      hasUnsavedChanges,
      refetchDataEntry,
      setScrollToRow,
      onAfterRowMutation: handleAfterRowMutation, // Add the callback here
    });

    // **PERFORMANCE OPTIMIZATION**: Streamlined focus management with React-based state
    // Replaced DOM query retry logic with efficient state management
    // Eliminates expensive DOM queries and setTimeout loops
    const focusCell = useCallback(
      (rowIndex: number, fieldIndex: number, subFieldIndex: number = 0) => {
        // **PERFORMANCE OPTIMIZATION**: Cache current rows reference to avoid multiple getValues calls
        const currentRows = getValues("rows");
        const targetRow = currentRows[rowIndex];

        if (!targetRow) {
          return;
        }

        setScrollToColumn(fieldIndex + 1);
        setScrollToRow(rowIndex + 1);

        // Generate the input ID based on field index and sub-field index
        let inputId = "";
        if (fieldIndex === 0) {
          // Depth From field
          inputId = `${targetRow.valueId || targetRow.id}-depthFrom`;
        } else if (fieldIndex === 1) {
          // Depth To field
          inputId = `${targetRow.valueId || targetRow.id}-depthTo`;
        } else {
          // Dynamic geology suite field
          const fieldIdx = fieldIndex - 2;
          const field = geologySuiteFields?.[fieldIdx];
          const dataEntryValue = targetRow.dataEntryValues?.[fieldIdx];

          if (field && dataEntryValue) {
            // For compound fields, append sub-field identifier
            if (isCompoundField(fieldIndex, targetRow)) {
              const subFieldSuffix = subFieldIndex === 0 ? "" : "-number";
              inputId = `${dataEntryValue.valueId}-${field?.geologyField?.type}${subFieldSuffix}`;
            } else {
              inputId = `${dataEntryValue.valueId}-${field?.geologyField?.type}`;
            }
          }
        }

        // **PERFORMANCE OPTIMIZATION**: Simplified focus with minimal retry logic
        // Since all columns are now always rendered, focus should be more reliable
        const attemptFocus = () => {
          const input = document.getElementById(inputId);
          if (input) {
            // Ensure the input is visible in the viewport before focusing
            input.scrollIntoView({
              behavior: "smooth",
              block: "nearest",
              inline: "nearest",
            });
            (input as HTMLInputElement)?.focus();
            setCurrentFocus({ rowIndex, fieldIndex, subFieldIndex });
          } else {
            // Single fallback attempt to focus any input in the cell
            const cellSelector = `[id^="${
              targetRow.valueId || targetRow.id
            }-"]`;
            const cellInputs = document.querySelectorAll(cellSelector);
            if (cellInputs.length > 0) {
              const firstInput = cellInputs[0] as HTMLInputElement;
              firstInput.scrollIntoView({
                behavior: "smooth",
                block: "nearest",
                inline: "nearest",
              });
              firstInput?.focus();
              setCurrentFocus({ rowIndex, fieldIndex, subFieldIndex: 0 });
            }
          }
        };

        // Immediate focus attempt since columns are always rendered
        attemptFocus();
      },
      [getValues, geologySuiteFields, isCompoundField]
    );

    // Extract action functions from the hook
    const {
      handleSave,
      addNewRowWithSave,
      copyRow,
      deleteRow,
      handleFieldChange,
    } = tableActions;

    // **PERFORMANCE OPTIMIZATION**: Memoized focus handler with stable reference
    const handleFocusCellOnDelete = useCallback(
      (rowIndex: number) => {
        // After deletion, set focus to the "depth from" cell of the previous row
        // or the new first row if the deleted row was the first.
        const currentRows = getValues("rows"); // getValues is from useForm
        if (currentRows.length > 0) {
          if (rowIndex === 0) {
            // Deleted the first row, focus the new first row's "depth from"
            focusCell(0, 0, 0);
          } else if (rowIndex > 0 && rowIndex <= currentRows.length) {
            // Deleted a row that was not the first, focus the previous row's "depth from"
            // The previous row is now at rowIndex - 1
            focusCell(rowIndex - 1, 0, 0);
          }
          // If rowIndexToDelete was > currentRows.length (e.g., last row deleted and currentRows is now empty),
          // no specific focus action is needed here as currentRows.length > 0 check handles it.
        }
        // If currentRows.length is 0, do nothing with focus.
      },
      [getValues, focusCell]
    );

    // **PERFORMANCE OPTIMIZATION**: Memoized action functions with stable references
    const addNewRowFinal = useCallback(
      () => addNewRowWithSave(),
      [addNewRowWithSave]
    );
    const copyRowFinal = useCallback(
      (rowIndex: number) => copyRow(rowIndex),
      [copyRow]
    );
    const deleteRowFinal = useCallback(
      (rowIndex: number) => {
        deleteRow(rowIndex);
        handleFocusCellOnDelete(rowIndex);
      },
      [deleteRow, handleFocusCellOnDelete]
    );

    // Enhanced unsaved changes modal handlers
    const handleSaveAndContinue = useCallback(async () => {
      try {
        await handleSave();
        setShowUnsavedModal(false);
        if (pendingNavigation) {
          pendingNavigation();
          setPendingNavigation(null);
        }
      } catch (error) {
        // Save failed, keep modal open
        toast.error("Failed to save changes. Please try again.");
      }
    }, [handleSave, pendingNavigation]);

    const handleDiscardAndContinue = useCallback(() => {
      setShowUnsavedModal(false);
      if (pendingNavigation) {
        pendingNavigation();
        setPendingNavigation(null);
      }
    }, [pendingNavigation]);

    const handleCancelNavigation = useCallback(() => {
      setShowUnsavedModal(false);
      setPendingNavigation(null);
    }, []);

    // **PERFORMANCE OPTIMIZATION**: Optimized keyboard navigation
    // Removed expensive getValues("rows") calls and improved efficiency
    // Reduces handleKeyDown execution time significantly
    const handleKeyDown = useCallback(
      (event: React.KeyboardEvent, rowIndex: number, fieldIndex: number) => {
        const totalFields = 2 + (geologySuiteFields?.length || 0); // 2 for depth fields + dynamic fields

        if (event.key === "Tab") {
          event.preventDefault();

          let nextRowIndex = rowIndex;
          let nextFieldIndex = fieldIndex;
          let nextSubFieldIndex = 0;

          // **PERFORMANCE OPTIMIZATION**: Cache current rows to avoid multiple getValues calls
          const currentRows = getValues("rows");
          const currentRow = currentRows[rowIndex];

          // Get current sub-field index from focus state
          const currentSubFieldIndex = currentFocus?.subFieldIndex || 0;

          if (event.shiftKey) {
            // Navigate backwards (Shift+Tab)
            if (
              isCompoundField(fieldIndex, currentRow) &&
              currentSubFieldIndex > 0
            ) {
              // Move to previous sub-field within the same compound field
              nextSubFieldIndex = currentSubFieldIndex - 1;
            } else if (fieldIndex > 0) {
              // Move to previous field
              nextFieldIndex = fieldIndex - 1;
              const targetRow = currentRows[nextRowIndex];
              if (isCompoundField(nextFieldIndex, targetRow)) {
                nextSubFieldIndex =
                  getSubFieldCount(nextFieldIndex, targetRow) - 1;
              } else {
                nextSubFieldIndex = 0;
              }
            } else if (rowIndex > 0) {
              // Move to previous row, last field
              nextRowIndex = rowIndex - 1;
              nextFieldIndex = totalFields - 1;
              const targetRow = currentRows[nextRowIndex];
              if (isCompoundField(nextFieldIndex, targetRow)) {
                nextSubFieldIndex =
                  getSubFieldCount(nextFieldIndex, targetRow) - 1;
              } else {
                nextSubFieldIndex = 0;
              }
            } else {
              // Already at first cell, do nothing
              return;
            }
          } else {
            // Navigate forwards (Tab)
            if (
              isCompoundField(fieldIndex, currentRow) &&
              currentSubFieldIndex <
                getSubFieldCount(fieldIndex, currentRow) - 1
            ) {
              // Move to next sub-field within the same compound field
              nextSubFieldIndex = currentSubFieldIndex + 1;
            } else if (fieldIndex < totalFields - 1) {
              // Move to next field in the same row
              nextFieldIndex = fieldIndex + 1;
              nextSubFieldIndex = 0;
            } else if (rowIndex < currentRows.length - 1) {
              // Move to next row, first field (depth from)
              nextRowIndex = rowIndex + 1;
              nextFieldIndex = 0;
              nextSubFieldIndex = 0;
              // Scroll to first dynamic geology suite field when moving to next row
              scrollToFirstDynamicField();
            } else {
              // Last cell of last row - add new row and focus its first field
              try {
                addNewRowFinal();
                // Scroll to first dynamic geology suite field to show dynamic fields
                scrollToFirstDynamicField();
                // Use a longer timeout to ensure the new row is fully rendered
                setTimeout(() => {
                  const updatedRows = getValues("rows");
                  if (updatedRows.length > currentRows.length) {
                    // Focus the depth from field (fieldIndex 0) of the new row
                    focusCell(0, 0, 0);
                  }
                }, 150);
                return;
              } catch (error) {
                return;
              }
            }
          }

          // Update focus state and focus the target cell
          setCurrentFocus({
            rowIndex: nextRowIndex,
            fieldIndex: nextFieldIndex,
            subFieldIndex: nextSubFieldIndex,
          });

          // Use a small delay to ensure proper focus transition
          setTimeout(() => {
            focusCell(nextRowIndex, nextFieldIndex, nextSubFieldIndex);
          }, 10);
        }
      },
      [
        geologySuiteFields?.length,
        addNewRowFinal,
        getValues,
        focusCell,
        currentFocus?.subFieldIndex,
        isCompoundField,
        getSubFieldCount,
        scrollToFirstDynamicField,
      ]
    );

    // **PERFORMANCE OPTIMIZATION**: Memoized cell focus handler with stable reference
    const handleCellFocus = useCallback(
      (rowIndex: number, fieldIndex: number, subFieldIndex: number = 0) => {
        setCurrentFocus({ rowIndex, fieldIndex, subFieldIndex });

        // Clear validation errors for the focused field to provide better UX
        // This allows users to see errors disappear as they start fixing them
        const currentRows = getValues("rows");
        const targetRow = currentRows[rowIndex];

        if (targetRow) {
          if (fieldIndex === 0) {
            // Depth From field
            clearErrors(`rows.${rowIndex}.depthFrom` as any);
          } else if (fieldIndex === 1) {
            // Depth To field
            clearErrors(`rows.${rowIndex}.depthTo` as any);
          } else {
            // Dynamic geology suite field - clear all possible field paths for this data entry value
            const dataEntryIndex = fieldIndex - 2;
            if (targetRow.dataEntryValues?.[dataEntryIndex]) {
              const basePath = `rows.${rowIndex}.dataEntryValues.${dataEntryIndex}`;
              clearErrors(`${basePath}.numberValue` as any);
              clearErrors(`${basePath}.description` as any);
              clearErrors(`${basePath}.dateValue` as any);
              clearErrors(`${basePath}.colourId` as any);
              clearErrors(`${basePath}.pickListItemId` as any);
              clearErrors(`${basePath}.rockTypeId` as any);
              clearErrors(`${basePath}.rockNodeId` as any);
              clearErrors(`${basePath}.value` as any);
            }
          }
        }
      },
      [getValues, clearErrors]
    );

    // **PERFORMANCE OPTIMIZATION**: Memoized cell blur handler with stable reference
    const handleCellBlur = useCallback(
      (_rowIndex: number, _fieldIndex: number) => {
        // Optional: Clear focus state when cell loses focus
        // setCurrentFocus(null);
      },
      []
    );

    const [loadingExport, setLoadingExport] = useState(false);
    const handleExport = async () => {
      if (!drillholeId || !globalProjectId) {
        return;
      }
      const response = await recoveryRequest.exportData({
        drillHoleIds: [drillholeId],
        exportType: ExportType.Calculations,
        projectId: globalProjectId,
      });
      if (response.state === "success") {
        setLoadingExport(false);
        toast.success("Export success");
        window.open(response.data ?? "", "_blank");
      } else {
        setLoadingExport(false);
        toast.error("Export failed");
      }
    };

    // **SERVER-SIDE FILTERING**: Use server-filtered data directly
    // No client-side filtering needed as API now handles search via keyWord parameter
    const filteredRows = fields;

    // **PERFORMANCE OPTIMIZATION**: Fixed columns dependencies
    // Removed watchedRows from dependency array to prevent unnecessary recalculations
    // Optimized table columns configuration with stable references
    const columns = useMemo(() => {
      // Calculate optimal action column width based on whether rows have status tags

      const baseColumns = [
        {
          title: "",
          key: "actions",
          width: 70,
          dataIndex: "actions",
          fixed: isMobile ? undefined : ("left" as const),
          render: (_: any, record: any, index: number) => (
            <ActionCell
              rowIndex={index}
              onDelete={deleteRowFinal}
              onCopy={copyRowFinal}
              rowStatus={record.rowStatus}
              loading={record.loading}
            />
          ),
        },
        {
          title: "Depth From",
          key: "depthFrom",
          width: 110,
          dataIndex: "depthFrom",
          fixed: isMobile ? undefined : ("left" as const),
          render: (_: any, record: LoggingRowData, rowIndex: number) => (
            <EditableCell
              key={`${record.id}-depthFrom`}
              control={control}
              rowIndex={rowIndex}
              fieldIdentifier="depthFrom"
              recordLoading={record.loading}
              onCellKeyDown={handleKeyDown}
              onCellFocus={handleCellFocus}
              onCellBlur={handleCellBlur}
              fieldIndexForNav={0}
              idPrefix={record.valueId || record.id} // Prefer valueId if available and stable
              onFieldChange={handleFieldChange}
            />
          ),
        },
        {
          title: "Depth To",
          key: "depthTo",
          width: 110,
          dataIndex: "depthTo",
          fixed: isMobile ? undefined : ("left" as const),
          render: (_: any, record: LoggingRowData, rowIndex: number) => (
            <EditableCell
              key={`${record.id}-depthTo`}
              control={control}
              rowIndex={rowIndex}
              fieldIdentifier="depthTo"
              recordLoading={record.loading}
              onCellKeyDown={handleKeyDown}
              onCellFocus={handleCellFocus}
              onCellBlur={handleCellBlur}
              fieldIndexForNav={1}
              idPrefix={record.valueId || record.id}
              onFieldChange={handleFieldChange}
            />
          ),
        },
      ];

      // **PERFORMANCE OPTIMIZATION**: Optimized cell renderer with Map-based lookups
      // Improved data lookup patterns in render functions for better performance
      const dynamicColumns =
        geologySuiteFields?.map((field: any, fieldIndex: number) => ({
          title: (field?.isMandatory ? "*" : "") + field?.name,
          key: `field-${field.id}`,
          dataIndex: `field-${field.id}`, // Add dataIndex for consistency
          width: getDynamicColumnWidth(field),
          render: (_: any, record: LoggingRowData, rowIndex: number) => {
            // **PERFORMANCE OPTIMIZATION**: Optimized data entry value lookup
            // Find the specific dataEntryValue using efficient search pattern
            let actualDataEntryValue: DataEntryValueData | undefined;
            let actualDataEntryValueIndex = -1;

            if (
              record.dataEntryValues &&
              Array.isArray(record.dataEntryValues)
            ) {
              // Find by geologysuiteFieldId, as this links to the `field` definition
              for (let i = 0; i < record.dataEntryValues.length; i++) {
                if (
                  record.dataEntryValues[i].geologysuiteFieldId === field.id
                ) {
                  actualDataEntryValue = record.dataEntryValues[i];
                  actualDataEntryValueIndex = i; // This is the index within the specific row's dataEntryValues
                  break;
                }
              }
            }

            if (!actualDataEntryValue || actualDataEntryValueIndex === -1) {
              return (
                <div
                  style={{ color: "orange", fontSize: "10px", padding: "2px" }}
                >
                  Cell Data Missing
                </div>
              );
            }

            return (
              <EditableCell
                key={`${record.id}-${field.id}`}
                control={control}
                rowIndex={rowIndex}
                fieldIdentifier={field.id} // The ID of the geologySuiteField
                geologySuiteField={field} // The full geologySuiteField object
                dataEntryValue={actualDataEntryValue} // The specific data value for this cell
                dataEntryValueIndex={actualDataEntryValueIndex} // The index of this dataEntryValue in record.dataEntryValues
                geologyFieldType={field?.geologyField?.type}
                recordLoading={record.loading}
                onCellKeyDown={handleKeyDown}
                onCellFocus={handleCellFocus}
                onCellBlur={handleCellBlur}
                fieldIndexForNav={fieldIndex + 2} // Navigation index based on geologySuiteFields.map
                allColours={colours}
                allRockTypes={rockTypes}
                idPrefix={record.valueId || record.id} // Unique prefix for input IDs
                onFieldChange={handleFieldChange}
              />
            );
          },
        })) || [];

      const finalColumns = [...baseColumns, ...dynamicColumns];

      return finalColumns;
    }, [
      isMobile,
      geologySuiteFields,
      control,
      deleteRowFinal,
      copyRowFinal,
      handleKeyDown,
      handleCellFocus,
      handleCellBlur,
      colours,
      rockTypes,
      handleFieldChange,
      // **PERFORMANCE OPTIMIZATION**: Removed watchedRows from dependencies
      // This prevents unnecessary columns recalculation on every form change
    ]);

    // **PERFORMANCE OPTIMIZATION**: Memoized row height function with stable reference
    const getRowHeight = useCallback(({ index }: { index: number }) => {
      if (index === 0) {
        // Header row height
        return 50;
      }
      // Data row height
      return FIXED_ROW_HEIGHT;
    }, []);

    // **PERFORMANCE OPTIMIZATION**: Memoized cell renderer with stable reference
    // Unified cell renderer for MultiGrid - handles both header and data rows
    const cellRenderer = useCallback(
      ({
        columnIndex,
        rowIndex,
        key,
        style,
      }: {
        columnIndex: number;
        rowIndex: number;
        key: string;
        style: React.CSSProperties;
      }) => {
        const column = columns[columnIndex];

        if (!column) {
          console.error(
            `Missing column at index ${columnIndex}. Total columns: ${columns.length}`
          );
          return (
            <div
              key={key}
              style={style}
              className="flex items-center justify-center text-red-500 border border-red-300"
            >
              Missing column (Col: {columnIndex})
            </div>
          );
        }

        // Header row (rowIndex === 0)
        if (rowIndex === 0) {
          return (
            <TableGeologyHeader
              columnIndex={columnIndex}
              column={column}
              style={style}
              fontSize={fontSize}
              isMobile={isMobile}
              key={key}
            />
          );
        }

        // Data rows (rowIndex > 0)
        const dataRowIndex = rowIndex - 1; // Adjust for header row
        const record = filteredRows[dataRowIndex] as LoggingRowData;

        if (!record) {
          return (
            <div
              key={key}
              style={style}
              className="flex items-center justify-center text-red-500 border border-red-300"
            >
              Missing data (Row: {dataRowIndex})
            </div>
          );
        }

        // Render the cell content using the column's render function
        let cellContent: React.ReactNode;
        try {
          if (column.render) {
            // For columns with custom render functions
            const dataValue = column.dataIndex
              ? record[column.dataIndex]
              : undefined;
            cellContent = column.render(dataValue, record, dataRowIndex);
          } else if (column.dataIndex) {
            // For simple data columns
            cellContent = record[column.dataIndex];
          } else {
            // Fallback for columns without dataIndex
            cellContent = `Col ${columnIndex}`;
          }
        } catch (error) {
          cellContent = <span className="text-red-500">Error</span>;
        }

        // Determine if this is the last fixed column for styling (matching header logic)
        const isLastFixedColumn = !isMobile && columnIndex === 2;

        return (
          <div
            key={key}
            style={{
              ...style,
              display: "flex",
              alignItems: "center",
              padding: "4px 8px",
              borderRight: "1px solid #e5e7eb",
              borderBottom: "1px solid #e5e7eb",
              fontSize: `${fontSize}px`,
              overflow: "hidden",
              boxSizing: "border-box",
              ...(isLastFixedColumn && {
                borderRight: "2px solid #d1d5db", // Stronger border for last fixed column
              }),
            }}
            className={`bg-white hover:bg-gray-50 ${
              record.loading ? "opacity-60" : ""
            } ${isLastFixedColumn ? "last-fixed-column" : ""}`}
          >
            <div className="virtualized-cell-content w-full">{cellContent}</div>
          </div>
        );
      },
      [columns, fontSize, isMobile, filteredRows]
    );

    // **PERFORMANCE OPTIMIZATION**: Memoized keyboard shortcuts with stable references
    useEffect(() => {
      const handleGlobalKeyDown = (event: KeyboardEvent) => {
        if (event.ctrlKey && event.key.toLowerCase() === "s") {
          event.preventDefault();
          handleSave();
        } else if (event.ctrlKey && event.key === "Enter") {
          event.preventDefault();
          addNewRowFinal();
        } else if (event.ctrlKey && event.key.toLowerCase() === "d") {
          event.preventDefault();
          if (currentFocus?.rowIndex !== undefined) {
            const rowIndexToDelete = currentFocus.rowIndex;
            deleteRowFinal(rowIndexToDelete);
          }
        } else if (event.ctrlKey && event.key.toLowerCase() === "y") {
          event.preventDefault();
          if (currentFocus?.rowIndex !== undefined) {
            copyRowFinal(currentFocus.rowIndex);
          }
        }
      };

      document.addEventListener("keydown", handleGlobalKeyDown);
      return () => document.removeEventListener("keydown", handleGlobalKeyDown);
    }, [
      handleSave,
      addNewRowFinal,
      deleteRowFinal,
      copyRowFinal,
      currentFocus?.rowIndex,
    ]);

    // **PERFORMANCE OPTIMIZATION**: Consolidated geology suite change effect
    // Effect to clear grid and state when geology suite changes.
    // This resolves the UI layout break when switch to another geology suite.
    useEffect(() => {
      const currentGeologySuiteId = geologySuiteDetail?.id;

      if (initialLoadDoneRef.current) {
        if (prevGeologySuiteIdRef.current !== currentGeologySuiteId) {
          // Geology suite has changed, clear previous state
          reset({ rows: [] });
          setDataToDelete([]);
          setCurrentFocus(null);
          setScrollToRow(undefined);
          setScrollToColumn(undefined);

          if (listRef.current) {
            listRef.current?.recomputeGridSize();
          }
        }
      } else {
        initialLoadDoneRef.current = true;
      }

      prevGeologySuiteIdRef.current = currentGeologySuiteId;
    }, [geologySuiteDetail?.id, reset]);

    // **REQUEST CONCURRENCY CONTROL**: Enhanced debounced load function
    // Combines debouncing with single active request enforcement
    // Prevents excessive API calls during rapid scroll events while ensuring only one request at a time
    const debouncedLoadDataEntries = useMemo(
      () =>
        debounce((skipCount: number, maxResultCount: number) => {
          // The loadDataEntries function now handles concurrency control internally
          loadDataEntries(skipCount, maxResultCount);
        }, 100),
      [loadDataEntries]
    );

    // **CHAT-LIKE SCROLLING**: Handle scroll events to detect user scrolling
    const handleScroll = useCallback(
      ({ scrollTop }: any) => {
        // **BIDIRECTIONAL SCROLLING**: Check if user is near the top (within 100px)
        const isNearTop = scrollTop < 100;

        // Load more data from top when user scrolls near the top
        if (isNearTop && hasMore) {
          let maxResultCount = 20;
          if (totalRows !== 0 && totalRows - filteredRows.length < 20) {
            maxResultCount = totalRows - filteredRows.length;
          }
          debouncedLoadDataEntries(filteredRows.length, maxResultCount);
        }
      },
      [hasMore, filteredRows.length, loadDataEntries, totalRows]
    );

    return (
      <div className="flex flex-col h-full">
        {/* Integrated Controls - Suite Selection and Search */}
        <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm flex-shrink-0 mb-4">
          <div className="flex flex-wrap items-center justify-between gap-4">
            <div className="flex flex-wrap items-center gap-4">
              {/* Combined Suite Selection - matching main logging component */}
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium text-gray-700">
                  Logging Suite:
                </span>
                <Select
                  allowClear
                  value={loggingSuiteIdParams}
                  key={loggingSuiteIdParams}
                  placeholder="Choose suite"
                  className="min-w-[250px]"
                  options={(geologySuites ?? [])
                    .map((attribute: any) => ({
                      label: attribute.name,
                      value: `geologySuites-${attribute.id}`,
                    }))
                    .concat(
                      (geotechSuites ?? []).map((geotechSuite: any) => ({
                        label: geotechSuite.name,
                        value: `geotechSuites-${geotechSuite.id}`,
                      }))
                    )}
                  onChange={handleLoggingSuiteChange}
                  disabled={loading || parentLoading}
                />
              </div>
            </div>

            {/* Search */}
            <div className="flex items-center gap-2">
              <SearchOutlined className="text-gray-500" />
              <Input
                placeholder="Search..."
                onChange={(e) => handleSearch(e.target.value)}
                className="w-64"
                allowClear
                disabled={loading || parentLoading}
              />
            </div>
          </div>
        </div>

        <TableGeologyBottom
          addNewRowFinal={addNewRowFinal}
          handleSave={handleSave}
          handleExport={handleExport}
          handleFontSizeChange={handleFontSizeChange}
          loading={loading}
          addRowLoading={addRowLoading}
          fontSize={fontSize}
          watchedRows={filteredRows}
          filteredRows={filteredRows}
          hasUnsavedChanges={hasUnsavedChanges}
          loadingExport={loadingExport}
          totalRows={totalRows}
        />

        {/* Virtualized Table Container */}
        <div
          className={`flex-1 w-full relative ${
            isMobile ? "overflow-x-auto" : ""
          }`}
          style={{
            minHeight: 0, // Important for flex child to shrink
          }}
        >
          <div className={`h-full ${isMobile ? "min-w-max" : "w-full"}`}>
            <div className={`relative h-full`}>
              <AutoSizerComponent>
                {({ width, height }: { width: number; height: number }) => (
                  <MultiGridComponent
                    ref={listRef}
                    width={width}
                    height={height}
                    columnWidth={({ index }: { index: number }) =>
                      columns[index]?.width || 150
                    }
                    columnCount={columns.length}
                    fixedColumnCount={isMobile ? 0 : 3} // Fixed columns for desktop only
                    fixedRowCount={1} // Header row
                    rowCount={filteredRows.length + 1} // +1 for header
                    rowHeight={getRowHeight}
                    cellRenderer={cellRenderer}
                    scrollToRow={scrollToRow}
                    scrollToColumn={scrollToColumn}
                    onScroll={handleScroll}
                    overscanRowCount={10}
                    overscanColumnCount={2}
                    style={{
                      outline: "none",
                    }}
                    className="virtualized-table"
                  />
                )}
              </AutoSizerComponent>
            </div>
          </div>
        </div>

        {/* Unsaved Changes Modal */}
        <Modal
          title={
            <div className="flex items-center">
              <ExclamationCircleOutlined className="text-orange-500 mr-2" />
              Unsaved Changes
            </div>
          }
          open={showUnsavedModal}
          onCancel={handleCancelNavigation}
          footer={[
            <Button key="cancel" onClick={handleCancelNavigation}>
              Cancel
            </Button>,
            <Button key="discard" onClick={handleDiscardAndContinue} danger>
              Discard Changes
            </Button>,
            <Button
              key="save"
              type="primary"
              icon={<SaveOutlined />}
              onClick={handleSaveAndContinue}
              loading={loading}
            >
              Save & Continue
            </Button>,
          ]}
          maskClosable={false}
          closable={false}
        >
          <p>
            You have unsaved changes. Do you want to save them before
            continuing?
          </p>
        </Modal>
      </div>
    );
  }
);

TableGeology.displayName = "TableGeology";
