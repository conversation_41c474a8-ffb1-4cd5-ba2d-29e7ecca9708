import { RequestState } from "@/common/configs/app.contants";
import { appRequest } from "@/common/configs/app.di-container";
import { getErrorMessage } from "@/utils/error.utils";

export interface DataEntryBody {
  id?: number;
  geologySuiteId: number;
  drillholeId: number;
  depthFrom?: any;
  depthTo?: any;
  dataEntryValues: any[];
}
const dataEntryRequest = {
  getMultipleDataEntryByDrillholes: async (params: {
    geologySuiteId: number;
    drillholeIds: string; // Array of IDs as string "[1,2,3]"
    geologySuiteFieldIds?: string; // Array of IDs as string "[1]"
    sortOrder: string;
  }) => {
    try {
      const response = await appRequest.get<any>(
        `/services/app/DataEntry/GetAllDataEntryByDrillhole`,
        params
      );
      return {
        state: RequestState.success,
        data: {
          items: response?.result?.items,
          pagination: {
            total: response?.result?.totalCount,
          },
        },
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  create: async (body: DataEntryBody) => {
    try {
      const response = await appRequest.post<any>(
        `/services/app/DataEntry/CreateDataEntry`,
        body
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  getAllDataEntry: async (params: {
    GeologySuiteId?: number;
    DrillholeName?: string;
    DrillholeId?: number;
    DepthFrom?: number;
    DepthTo?: number;
    skipCount?: number;
    maxResultCount?: number;
    sortOrder?: string;
    keyWord?: string;
  }) => {
    try {
      const response = await appRequest.get<any>(
        `/services/app/DataEntry/GetAllDataEntry`,
        {
          ...params,
          maxResultCount: params?.maxResultCount ?? 1000,
        }
      );
      return {
        state: RequestState.success,
        data: {
          items: response?.result?.items,
          pagination: {
            current:
              Math.floor(
                (params?.skipCount ?? 1) / (params?.maxResultCount ?? 10)
              ) + 1,
            pageSize: params?.maxResultCount ?? 10,
            total: response?.result?.totalCount,
          },
        },
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  delete: async (params: { id: string }) => {
    try {
      const response = await appRequest.delete<any>(
        `/services/app/DataEntry/DeleteGeologyDataPoint?Id=${params.id}`
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },

  update: async (body: DataEntryBody) => {
    try {
      const response = await appRequest.put<any>(
        `/services/app/DataEntry/UpdateDataEntry`,
        body
      );

      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  updateDepth: async (body: {
    id: number;
    depthFrom: number | null;
    depthTo: number | null;
  }) => {
    try {
      const response = await appRequest.put<any>(
        `/services/app/DataEntry/UpdateDepth`,
        body
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  updateDataEntryValue: async (body: {
    valueId: number;
    fieldType: number;
    colorId?: number;
    dateValue?: string;
    description?: string;
    numberValue?: number;
    pickListItemId?: number;
    rockTypeId?: number;
  }) => {
    try {
      const response = await appRequest.put<any>(
        `/services/app/DataEntry/UpdateDataEntryValue`,
        body
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
};

export default dataEntryRequest;
