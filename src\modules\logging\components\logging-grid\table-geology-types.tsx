import { FieldType } from "@/modules/geology-suite-field/const/enum";
import { RowStatus } from "../render-field-types";

// Enums and constants
export enum ErrorDetail {
  INVALID = "INVALID",
}

export interface LoggingGridProps {
  refetchDataEntry: () => Promise<void>;
}

// LoggingGridTopProps removed - functionality moved to individual table components

// Props for the bottom row component (actions and controls)
export interface TableGeologyBottomProps {
  addNewRowFinal: () => void;
  handleSave: () => void;
  handleExport: () => void;
  handleFontSizeChange: (increment: boolean) => void;
  loading: boolean;
  addRowLoading: boolean;
  fontSize: number;
  watchedRows: LoggingRowData[];
  filteredRows: LoggingRowData[];
  hasUnsavedChanges: boolean;
  loadingExport: boolean;
  totalRows: number;
}

// Props for the geology table component
export interface TableGeologyProps {
  searchText: string;
  fontSize: number;
  dynamicWidth: number;
  handleFontSizeChange: (increment: boolean) => void;
  refetchDataEntry: () => Promise<void>;
  // New props for integrated controls
  loggingSuiteIdParams: string;
  geologySuites: any[];
  geotechSuites: any[];
  loading: boolean;
  handleLoggingSuiteChange: (value: string) => void;
  handleSearch: (value: string) => void;
}

// Props for the geotech display table component
export interface TableGeotechDisplayProps {
  searchText: string;
  fontSize: number;
  dynamicWidth: number;
  handleFontSizeChange: (increment: boolean) => void;
  // New props for integrated controls
  loggingSuiteIdParams: string;
  geologySuites: any[];
  geotechSuites: any[];
  loading: boolean;
  handleLoggingSuiteChange: (value: string) => void;
  handleSearch: (value: string) => void;
}

// Props for the geology table header component
export interface TableGeologyHeaderProps {
  columnIndex: number;
  column: any;
  style: React.CSSProperties;
  fontSize: number;
  isMobile: boolean;
  key: string;
}

export interface LoggingFormData {
  rows: LoggingRowData[];
}

export interface LoggingRowData {
  id: string;
  valueId: string;
  depthFrom: number;
  depthTo: number;
  rowStatus: RowStatus;
  geologySuiteId: number;
  drillholeId: number;
  loading?: boolean;
  errorDetail?: ErrorDetail;
  dataEntryValues: DataEntryValueData[];
}

export interface DataEntryValueData {
  fieldType: FieldType;
  geologysuiteFieldId: string;
  isMandatory: boolean;
  valueId: string;
  value: any;
  numberId?: number;
  numberValue?: number;
  pickListItemId?: number;
  rockTypeId?: number;
  colourId?: number;
  dateValue?: string;
  description?: string;
  number?: { id?: number; unit?: { code?: string } };
  rockNodeId?: number;
}
